#!/usr/bin/env python3
"""
Complete example showing how to use the AI News Collector with the news pipeline.

This script demonstrates:
1. Loading configuration from YAML
2. Collecting news from multiple sources
3. Processing articles through the news extraction pipeline
4. Saving results

Usage:
    python complete_example.py

Requirements:
    - OPENROUTER_API_KEY environment variable set
    - TAVILY_API_KEY environment variable set
"""

import os
import sys
import asyncio
import yaml
from datetime import datetime
from typing import List

# Add the parent directories to sys.path
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai_source_parser'))

from ai_news_collector.models import NewsCollectionConfig
from ai_news_collector.agents import create_news_collector
from ai_source_parser.news_pipeline_refactored import NewsExtractor, ExtractInput


async def main():
    """Main example function."""
    print("🔧 Setting up AI News Collection and Processing Pipeline")
    print("=" * 60)
    
    # Check environment variables
    if not os.getenv('OPENROUTER_API_KEY'):
        print("❌ Missing OPENROUTER_API_KEY environment variable")
        return
    
    if not os.getenv('TAVILY_API_KEY'):
        print("❌ Missing TAVILY_API_KEY environment variable")
        return
    
    # Load configuration
    try:
        with open('ai_news_collector/config.yaml', 'r') as f:
            config_data = yaml.safe_load(f)
        config = NewsCollectionConfig(**config_data)
        print(f"✅ Loaded configuration with {len(config.sources)} sources")
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return
    
    # Create news collector
    try:
        collector = create_news_collector()
        print("✅ Created news collector")
    except Exception as e:
        print(f"❌ Error creating collector: {e}")
        return
    
    # Create news extractor for processing
    try:
        extractor = NewsExtractor()
        print("✅ Created news extractor")
    except Exception as e:
        print(f"❌ Error creating extractor: {e}")
        return
    
    print("\n🔍 Starting news collection...")
    print("-" * 40)
    
    # Collect news from all sources
    try:
        result = await collector.collect_news(config)
        print(f"✅ Collection completed")
        print(f"📰 Total articles found: {len(result.all_articles)}")
        print(f"✅ Successful sources: {result.successful_sources}")
        print(f"❌ Failed sources: {result.failed_sources}")
        
        if result.failed_sources > 0:
            print("\n⚠️  Some sources failed:")
            for extraction_result in result.results:
                if not extraction_result.success:
                    print(f"   - {extraction_result.source_id}: {extraction_result.error_message}")
        
    except Exception as e:
        print(f"❌ Error during collection: {e}")
        return
    
    if not result.all_articles:
        print("\n📭 No articles collected")
        return
    
    print(f"\n📄 Processing {len(result.all_articles)} articles through extraction pipeline...")
    print("-" * 60)
    
    # Process articles through the news extraction pipeline
    processed_items = []
    
    for i, article in enumerate(result.all_articles, 1):
        print(f"Processing article {i}/{len(result.all_articles)}: {article.title[:50]}...")
        
        try:
            # Create extraction input
            extract_input = ExtractInput(
                url=article.url,
                outlet=article.source,
                raw_content=article.content
            )
            
            # Extract structured news item
            news_item = extractor.extract(extract_input)
            processed_items.append(news_item)
            
            print(f"  ✅ Category: {news_item.classification.category.value}")
            print(f"  ✅ Type: {news_item.classification.type.value}")
            print(f"  ✅ Title: {news_item.content.title}")
            
        except Exception as e:
            print(f"  ❌ Error processing article: {e}")
    
    print(f"\n🎉 Processing complete!")
    print(f"📊 Successfully processed: {len(processed_items)}/{len(result.all_articles)} articles")
    
    # Summary by category
    if processed_items:
        from collections import Counter
        categories = Counter(item.classification.category.value for item in processed_items)
        types = Counter(item.classification.type.value for item in processed_items)
        
        print(f"\n📈 Summary by Category:")
        for category, count in categories.most_common():
            print(f"   {category}: {count}")
        
        print(f"\n📋 Summary by Type:")
        for item_type, count in types.most_common():
            print(f"   {item_type}: {count}")
    
    # Show sample results
    if processed_items:
        print(f"\n📰 Sample Results:")
        print("-" * 40)
        for i, item in enumerate(processed_items[:3], 1):
            print(f"\n{i}. {item.content.title}")
            print(f"   URL: {item.source.url}")
            print(f"   Category: {item.classification.category.value}")
            print(f"   Type: {item.classification.type.value}")
            print(f"   Jurisdictions: {', '.join(item.classification.jurisdictions) if item.classification.jurisdictions else 'None'}")
            print(f"   Summary: {item.content.summary[:100]}...")


if __name__ == "__main__":
    print("🚀 AI News Collection & Processing Example")
    print("==========================================\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
