from __future__ import annotations

import asyncio
import os
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

import dotenv
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider
from pydantic_ai.toolsets import FunctionToolset

dotenv.load_dotenv()

from .models import (
    NewsSource,
    RawArticle,
    ExtractionResult,
    NewsCollectionConfig,
    NewsCollectionResult,
    ExtractionMode,
    SourceType,
    TavilySearchParams,
    ExtractUrlParams,
    DateFilterParams,
    UrlFilterParams,
)
from .tools import ALL_NEWS_TOOL_FUNCTIONS


class ModelManager:
    """Singleton model manager to avoid creating multiple model instances."""
    
    _instance: Optional[OpenAIChatModel] = None
    
    @classmethod
    def get_model(cls, model_name: str = "openai/gpt-4o-mini") -> OpenAIChatModel:
        if cls._instance is None:
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                raise RuntimeError("Missing OPENROUTER_API_KEY in environment")
            
            cls._instance = OpenAIChatModel(
                model_name,
                provider=OpenRouterProvider(api_key=api_key),
            )
        return cls._instance


class NewsExtractionAgent:
    """Agent responsible for extracting news from a single source."""
    
    def __init__(self, model_name: str = "openai/gpt-4o-mini"):
        self.model = ModelManager.get_model(model_name)
        
        # Create toolset with all tools
        self.toolset = FunctionToolset()
        for tool_func in ALL_NEWS_TOOL_FUNCTIONS:
            self.toolset.add_function(tool_func)
        
        # Create the agent
        self.agent = Agent(
            model=self.model,
            toolsets=[self.toolset],
            deps_type=NewsSource,
            output_type=ExtractionResult,
            system_prompt="""You are a specialized news extraction agent focused on carbon regulation, clean energy, and climate policy news.

**Your Mission**: Extract relevant, high-quality news articles from sources based on specific instructions.

**Available Tools**:
- tavily_search: Search for news using Tavily search engine
- extract_urls: Extract full content from specific URLs
- crawl_website: Crawl a website to find article URLs
- filter_by_date: Filter articles by publication date
- filter_urls: Filter URLs based on configuration
- extract_article_urls: Extract article URLs from webpage content
- check_relevance: Check if content is relevant

**Step-by-Step Process**:

1. **Analyze the source**: Review the source configuration, extraction_mode, and specific instructions.

2. **Choose the right tool**:
   - For extraction_mode "tavily_search": Use tavily_search tool
   - For extraction_mode "website_crawl": Use crawl_website then extract_urls
   - For extraction_mode "direct_extract": Use extract_urls directly

3. **Extract relevant articles**: 
   - Focus on articles related to: carbon markets, carbon pricing, emissions trading, clean energy, renewable energy, climate regulation, environmental policy
   - Prioritize recent articles (within the time_window_hours)
   - Look for substantial articles with meaningful content (avoid brief mentions or summaries)

4. **Process results thoroughly**:
   - When using tavily_search, examine ALL returned results and extract the ones relevant to carbon/climate/energy topics
   - Use the FULL content from raw_content field when available
   - Create RawArticle objects with complete information
   - Respect the max_articles limit but aim to find quality articles up to that limit

5. **Quality filtering**:
   - Ensure articles are substantive (not just headlines or brief mentions)
   - Verify articles are actually about carbon/climate/energy topics (not just containing keywords)
   - Check publication dates fall within the time window
   - Use outlet names and proper source attribution

**Critical Requirements**:
- ALWAYS use the full raw_content when available from tool responses
- ALWAYS create proper RawArticle objects with correct source_id, outlet, and complete content
- PRIORITIZE quality and relevance over quantity
- If tavily_search returns 15 results, examine each one for relevance
- Return ExtractionResult with success=True and populated articles list

**Content Focus Areas**:
- Carbon pricing and carbon markets
- Emissions trading systems (EU ETS, etc.)
- Clean energy policy and regulation
- Renewable energy developments
- Climate change legislation
- Environmental compliance and enforcement
- Green finance and sustainable investing
- Carbon border adjustments and trade policy"""
        )
    
    async def extract_from_source(self, source: NewsSource) -> ExtractionResult:
        """Extract articles from a single news source."""
        print(f"      🔧 Starting extraction for {source.name}")
        try:
            prompt = f"""
Extract news articles from this source:

Source: {source.name}
Type: {source.source_type}
Extraction Mode: {source.extraction_mode}
URL: {source.url}
Search Query: {source.search_query}
Time Window: {source.time_window_hours} hours
Max Articles: {source.max_articles}

Instructions: {source.extraction_instructions}

Additional filters:
- Include domains: {source.include_domains}
- Exclude domains: {source.exclude_domains}
- Keywords: {source.keywords}

Please follow the instructions and extract relevant articles within the time window.
"""
            
            print(f"      🤖 Sending request to AI agent...")
            result = await self.agent.run(prompt, deps=source)
            print(f"      ✅ AI agent completed, processing result...")
            return result.output
            
        except Exception as e:
            print(f"      💥 Exception in extract_from_source: {str(e)}")
            import traceback
            traceback.print_exc()
            return ExtractionResult(
                source_id=source.id,
                articles=[],
                success=False,
                error_message=str(e)
            )


class NewsSourceAgent:
    """Agent that acts as a tool for the main coordinator."""
    
    def __init__(self, extraction_agent: NewsExtractionAgent):
        self.extraction_agent = extraction_agent
    
    async def __call__(self, ctx: RunContext[NewsCollectionConfig], source_id: str) -> ExtractionResult:
        """Extract from a specific source - this agent acts as a tool."""
        config = ctx.deps
        source = None
        
        for s in config.sources:
            if s.id == source_id and s.enabled:
                source = s
                break
        
        if not source:
            return ExtractionResult(
                source_id=source_id,
                articles=[],
                success=False,
                error_message=f"Source {source_id} not found or disabled"
            )
        
        return await self.extraction_agent.extract_from_source(source)


class NewsCoordinatorAgent:
    """Main coordinator agent that orchestrates news collection from multiple sources."""
    
    def __init__(self, model_name: str = "openai/gpt-4o-mini"):
        self.model = ModelManager.get_model(model_name)
        self.extraction_agent = NewsExtractionAgent(model_name)
        self.source_agent = NewsSourceAgent(self.extraction_agent)
        
        # Create the coordinator agent
        self.agent = Agent(
            model=self.model,
            deps_type=NewsCollectionConfig,
            output_type=NewsCollectionResult,
            system_prompt="""You are a news collection coordinator. Your job is to coordinate the collection of news from multiple sources.

You have access to a tool called 'extract_from_source' that can extract articles from individual news sources.

Your responsibilities:
1. Analyze the configuration and identify enabled sources
2. Respect the max_concurrent_sources limit when processing
3. Use the extract_from_source tool for each enabled source
4. Compile results into a NewsCollectionResult
5. Handle any errors gracefully

Process sources efficiently and provide a comprehensive summary."""
        )
        
        # Register the source agent as a tool
        @self.agent.tool
        async def extract_from_source(ctx: RunContext[NewsCollectionConfig], source_id: str) -> ExtractionResult:
            """Extract articles from a specific news source."""
            return await self.source_agent(ctx, source_id)
    
    async def collect_news(self, config: NewsCollectionConfig) -> NewsCollectionResult:
        """Collect news from all configured sources."""
        try:
            enabled_sources = [s for s in config.sources if s.enabled]
            
            if not enabled_sources:
                result = NewsCollectionResult()
                result.finalize()
                return result
            
            prompt = f"""
Collect news from all enabled sources in the configuration.

There are {len(enabled_sources)} enabled sources:
{[s.id for s in enabled_sources]}

Please process each source using the extract_from_source tool and compile the results.
Respect the max_concurrent_sources limit of {config.max_concurrent_sources}.
"""
            
            result = await self.agent.run(prompt, deps=config)
            return result.output
            
        except Exception as e:
            result = NewsCollectionResult()
            result.extraction_results = [
                ExtractionResult(
                    source_id="error",
                    articles=[],
                    success=False,
                    error_message=f"Coordinator error: {str(e)}"
                )
            ]
            result.finalize()
            return result


# Simplified single-agent approach for direct use
class SimpleNewsCollector:
    """Simple news collector that processes sources sequentially."""
    
    def __init__(self, model_name: str = "openai/gpt-4o-mini"):
        self.extraction_agent = NewsExtractionAgent(model_name)
        print(f"🤖 Created SimpleNewsCollector with model: {model_name}")
    
    async def collect_news(self, config: NewsCollectionConfig) -> NewsCollectionResult:
        """Collect news from all configured sources."""
        print(f"\n🚀 Starting news collection from {len(config.sources)} total sources")
        result = NewsCollectionResult()
        
        enabled_sources = [s for s in config.sources if s.enabled]
        print(f"✅ Found {len(enabled_sources)} enabled sources:")
        for i, source in enumerate(enabled_sources, 1):
            print(f"   {i}. {source.name} ({source.id}) - {source.extraction_mode.value}")
        
        if not enabled_sources:
            print("⚠️  No enabled sources found, returning empty result")
            result.finalize()
            return result
        
        # Process sources with concurrency limit
        semaphore = asyncio.Semaphore(config.max_concurrent_sources)
        print(f"🔄 Processing with max concurrency: {config.max_concurrent_sources}")
        
        async def process_source(source: NewsSource) -> ExtractionResult:
            async with semaphore:
                print(f"\n🔍 Processing source: {source.name} ({source.id})")
                print(f"   📊 Mode: {source.extraction_mode.value}")
                print(f"   ⏰ Time window: {source.time_window_hours} hours")
                print(f"   📄 Max articles: {source.max_articles}")
                
                try:
                    extraction_result = await self.extraction_agent.extract_from_source(source)
                    
                    if extraction_result.success:
                        print(f"   ✅ Extraction successful: {len(extraction_result.articles)} articles found")
                    else:
                        print(f"   ❌ Extraction failed: {extraction_result.error_message}")
                        return extraction_result
                    
                    # Apply global filters
                    print(f"   🔧 Applying global filters...")
                    filtered_articles = []
                    for article in extraction_result.articles:
                        # Apply global content length filters
                        content_len = len(article.content)
                        print(f"      📄 Article: '{article.title[:60]}...' ({content_len} chars)")
                        print(f"         Content preview: {article.content[:200]}...")
                        
                        if config.min_content_length <= content_len <= config.max_content_length:
                            # Check global exclude domains
                            if config.global_exclude_domains:
                                from urllib.parse import urlparse
                                domain = urlparse(str(article.url)).netloc.lower()
                                if not any(excluded.lower() in domain for excluded in config.global_exclude_domains):
                                    filtered_articles.append(article)
                                    print(f"         ✅ Article accepted")
                                else:
                                    print(f"         🚫 Filtered out article from excluded domain: {domain}")
                            else:
                                filtered_articles.append(article)
                                print(f"         ✅ Article accepted")
                        else:
                            print(f"         🚫 Filtered out article due to content length: {content_len} chars (min: {config.min_content_length}, max: {config.max_content_length})")
                    
                    extraction_result.articles = filtered_articles
                    extraction_result.total_filtered = len(filtered_articles)
                    print(f"   📊 After filtering: {len(filtered_articles)} articles remain")
                    return extraction_result
                    
                except Exception as e:
                    print(f"   💥 Exception during processing: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    return ExtractionResult(
                        source_id=source.id,
                        articles=[],
                        success=False,
                        error_message=f"Processing exception: {str(e)}"
                    )
        
        # Process all sources concurrently
        print(f"\n🔄 Starting concurrent processing of {len(enabled_sources)} sources...")
        tasks = [process_source(source) for source in enabled_sources]
        extraction_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle results and exceptions
        print(f"\n📊 Processing results...")
        for i, extraction_result in enumerate(extraction_results):
            source_name = enabled_sources[i].name
            if isinstance(extraction_result, Exception):
                print(f"❌ {source_name}: Exception occurred - {str(extraction_result)}")
                result.extraction_results.append(
                    ExtractionResult(
                        source_id=enabled_sources[i].id,
                        articles=[],
                        success=False,
                        error_message=f"Task exception: {str(extraction_result)}"
                    )
                )
            else:
                status = "✅" if extraction_result.success else "❌"
                article_count = len(extraction_result.articles)
                print(f"{status} {source_name}: {article_count} articles")
                if not extraction_result.success:
                    print(f"      Error: {extraction_result.error_message}")
                result.extraction_results.append(extraction_result)
        
        result.finalize()
        print(f"\n🎯 Collection completed:")
        print(f"   📊 Total sources processed: {result.total_sources_processed}")
        print(f"   ✅ Successful sources: {result.total_sources_successful}")
        print(f"   📰 Total articles collected: {result.total_articles_collected}")
        return result


# Factory function to create news collector
def create_news_collector(use_multi_agent: bool = False, model_name: str = "openai/gpt-4o-mini"):
    """Create a news collector instance."""
    if use_multi_agent:
        return NewsCoordinatorAgent(model_name)
    else:
        return SimpleNewsCollector(model_name)


# Example agent that can be used as a tool in other systems
class NewsCollectorTool:
    """News collector that can be used as a tool by other agents."""
    
    def __init__(self, collector: Optional[SimpleNewsCollector] = None):
        self.collector = collector or SimpleNewsCollector()
    
    async def __call__(self, ctx: RunContext, config_dict: Dict[str, Any]) -> List[RawArticle]:
        """Collect news and return articles - tool interface."""
        try:
            config = NewsCollectionConfig(**config_dict)
            result = await self.collector.collect_news(config)
            return result.all_articles
        except Exception as e:
            # Return empty list on error - let the calling agent handle it
            return []


# Tool definition for use in other agents
def create_news_collector_tool(collector: Optional[SimpleNewsCollector] = None) -> NewsCollectorTool:
    """Create a news collector tool for use in other agents."""
    return NewsCollectorTool(collector)
