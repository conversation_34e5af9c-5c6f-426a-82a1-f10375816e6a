#!/usr/bin/env python3
"""
Simple test script for the news collection system.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent))

from ai_news_collector.models import NewsSource, NewsCollectionConfig, SourceType, ExtractionMode
from ai_news_collector.agents import SimpleNewsCollector


async def test_basic_collection():
    """Test basic news collection functionality."""
    print("🧪 Testing basic news collection...")
    
    # Create a simple test configuration
    test_source = NewsSource(
        id="test_carbon_search",
        name="Test Carbon Markets Search",
        source_type=SourceType.WEB_SEARCH,
        extraction_mode=ExtractionMode.TAVILY_SEARCH,
        search_query="carbon markets news",
        time_window_hours=24,
        max_articles=3,
        extraction_instructions="""
        Search for recent news about carbon markets.
        Use the tavily_search tool to find 3 recent articles.
        Extract the title, URL, and content of each article.
        Make sure articles are from the last 24 hours.
        """,
        keywords=["carbon", "market", "trading"],
        enabled=True
    )
    
    config = NewsCollectionConfig(
        sources=[test_source],
        default_time_window_hours=24,
        max_concurrent_sources=1,
        retry_attempts=1
    )
    
    # Test collection
    collector = SimpleNewsCollector()
    
    try:
        result = await collector.collect_news(config)
        
        print(f"✅ Collection completed:")
        print(f"   Sources processed: {result.total_sources_processed}")
        print(f"   Sources successful: {result.total_sources_successful}")
        print(f"   Articles collected: {result.total_articles_collected}")
        
        if result.extraction_results:
            for extraction_result in result.extraction_results:
                print(f"   Source {extraction_result.source_id}: {len(extraction_result.articles)} articles")
                if not extraction_result.success:
                    print(f"   Error: {extraction_result.error_message}")
        
        if result.all_articles:
            print(f"\n📰 Sample articles:")
            for i, article in enumerate(result.all_articles[:2]):  # Show first 2
                print(f"   {i+1}. {article.title or 'Untitled'}")
                print(f"      URL: {article.url}")
                print(f"      Content length: {len(article.content)} chars")
        
        return result.total_articles_collected > 0
        
    except Exception as e:
        print(f"❌ Collection failed: {str(e)}")
        return False


async def main():
    """Main test function."""
    print("🚀 AI News Collector Test Suite\n")
    
    # Check environment
    if not os.getenv('TAVILY_API_KEY'):
        print("⚠️  Warning: TAVILY_API_KEY not set - some features may not work")
    
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not set - cannot run tests")
        return
    
    # Run tests
    success = await test_basic_collection()
    
    if success:
        print("\n✅ Tests passed!")
    else:
        print("\n❌ Tests failed!")


if __name__ == "__main__":
    asyncio.run(main())
