from __future__ import annotations

import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse

import httpx
from tavily import TavilyClient

from pydantic_ai import RunContext

from .models import (
    TavilySearchParams,
    ExtractUrlParams,
    DateFilterParams,
    UrlFilterParams,
    RawArticle,
    ArticleFilterResult,
)


class NewsCollectionTools:
    """Collection of tools for news collection agents."""
    
    def __init__(self, tavily_api_key: Optional[str] = None):
        self.tavily_api_key = tavily_api_key or os.getenv('TAVILY_API_KEY')
        if self.tavily_api_key:
            self.tavily_client = TavilyClient(self.tavily_api_key)
        else:
            self.tavily_client = None


def tavily_search_tool(ctx: RunContext, params: TavilySearchParams) -> Dict[str, Any]:
    """Search for news articles using Tavily."""
    tools = NewsCollectionTools()
    
    if not tools.tavily_client:
        return {
            "success": False,
            "error": "Tavily API key not configured",
            "results": []
        }
    
    try:
        print(f"         🔍 Tavily search: '{params.query}' (max_results: {params.max_results})")
        response = tools.tavily_client.search(
            query=params.query,
            topic="news",
            search_depth="advanced",
            time_range=params.time_range,
            max_results=params.max_results,
            include_domains=params.include_domains or [],
            exclude_domains=params.exclude_domains or [],
            include_raw_content=True
        )
        
        print(f"         📊 Tavily returned {len(response.get('results', []))} results")
        
        results = []
        for i, result in enumerate(response.get('results', []), 1):
            raw_content = result.get('raw_content', '')
            content = result.get('content', '')
            final_content = raw_content if raw_content else content
            
            print(f"         📄 Result {i}: '{result.get('title', 'No title')[:50]}...'")
            print(f"            🔗 URL: {result.get('url')}")
            print(f"            📝 Content length: raw_content={len(raw_content)}, content={len(content)}, final={len(final_content)}")
            
            results.append({
                'url': result.get('url'),
                'title': result.get('title'),
                'content': final_content,
                'published_date': result.get('published_date'),
                'score': result.get('score', 0.0)
            })
        
        return {
            "success": True,
            "results": results,
            "total_found": len(results)
        }
        
    except Exception as e:
        print(f"         💥 Tavily search error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "results": []
        }


def extract_urls_tool(ctx: RunContext, params: ExtractUrlParams) -> Dict[str, Any]:
    """Extract content from specific URLs."""
    tools = NewsCollectionTools()
    
    if not tools.tavily_client:
        return {
            "success": False,
            "error": "Tavily API key not configured",
            "results": []
        }
    
    try:
        print(f"         🔗 Extracting content from {len(params.urls)} URLs")
        response = tools.tavily_client.extract(
            urls=params.urls,
            extract_depth=params.extract_depth
        )
        
        results = []
        for i, result in enumerate(response.get('results', []), 1):
            raw_content = result.get('raw_content', '')
            content = result.get('content', '')
            final_content = raw_content if raw_content else content
            
            print(f"         📄 Extract {i}: {result.get('url')}")
            print(f"            📝 Content length: raw_content={len(raw_content)}, content={len(content)}, final={len(final_content)}")
            print(f"            ✅ Success: {result.get('success', False)}")
            
            results.append({
                'url': result.get('url'),
                'title': result.get('title'),
                'content': final_content,
                'success': result.get('success', False)
            })
        
        return {
            "success": True,
            "results": results,
            "total_extracted": len(results)
        }
        
    except Exception as e:
        print(f"         💥 Extract URLs error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "results": []
        }


def crawl_website_tool(ctx: RunContext, base_url: str, max_pages: int = 10) -> Dict[str, Any]:
    """Crawl a website to find article URLs."""
    try:
        # Simple website crawling using httpx
        # In a real implementation, you might want to use BeautifulSoup or Scrapy
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (compatible; NewsBot/1.0)'
        }
        
        found_urls = []
        
        async def crawl_page(url: str):
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(url, headers=headers, timeout=10.0)
                    if response.status_code == 200:
                        content = response.text
                        
                        # Simple regex to find article URLs
                        # This would need to be more sophisticated for real use
                        url_pattern = r'href=["\']([^"\']*(?:article|story|news|post)[^"\']*)["\']'
                        matches = re.findall(url_pattern, content, re.IGNORECASE)
                        
                        for match in matches:
                            if match.startswith('/'):
                                full_url = f"{base_url.rstrip('/')}{match}"
                            elif match.startswith('http'):
                                full_url = match
                            else:
                                continue
                            
                            if full_url not in found_urls:
                                found_urls.append(full_url)
                                
                except Exception as e:
                    print(f"Error crawling {url}: {e}")
        
        # For now, just return the base URL since async crawling is complex
        # In a real implementation, you'd want to implement proper async crawling
        return {
            "success": True,
            "urls": [base_url],
            "total_found": 1
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "urls": []
        }


def filter_by_date_tool(ctx: RunContext, params: DateFilterParams) -> ArticleFilterResult:
    """Filter articles based on publication date."""
    cutoff_time = datetime.now() - timedelta(hours=params.cutoff_hours)
    
    # Try to extract date from content
    # This is a simplified implementation
    date_patterns = [
        r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
        r'(\d{1,2}/\d{1,2}/\d{4})',  # MM/DD/YYYY
        r'(\d{1,2}-\d{1,2}-\d{4})',  # MM-DD-YYYY
    ]
    
    for pattern in date_patterns:
        matches = re.findall(pattern, params.content)
        if matches:
            try:
                # Try to parse the first match
                date_str = matches[0]
                if '-' in date_str and len(date_str) == 10:  # YYYY-MM-DD
                    article_date = datetime.strptime(date_str, '%Y-%m-%d')
                elif '/' in date_str:  # MM/DD/YYYY
                    article_date = datetime.strptime(date_str, '%m/%d/%Y')
                elif '-' in date_str and len(date_str) == 10:  # MM-DD-YYYY
                    article_date = datetime.strptime(date_str, '%m-%d-%Y')
                else:
                    continue
                
                if article_date >= cutoff_time:
                    return ArticleFilterResult(
                        included=True,
                        reason=f"Article date {article_date.isoformat()} is within {params.cutoff_hours} hours",
                        confidence=0.8
                    )
                else:
                    return ArticleFilterResult(
                        included=False,
                        reason=f"Article date {article_date.isoformat()} is older than {params.cutoff_hours} hours",
                        confidence=0.8
                    )
            except ValueError:
                continue
    
    # If no date found, include with low confidence
    return ArticleFilterResult(
        included=True,
        reason="No publication date found, including by default",
        confidence=0.3
    )


def filter_urls_tool(ctx: RunContext, params: UrlFilterParams) -> List[str]:
    """Filter URLs based on source configuration."""
    filtered_urls = []
    source_config = params.source_config
    
    for url in params.urls:
        should_include = True
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()
        
        # Check include domains
        if source_config.get('include_domains'):
            if not any(included_domain.lower() in domain for included_domain in source_config['include_domains']):
                should_include = False
                continue
        
        # Check exclude domains
        if source_config.get('exclude_domains'):
            if any(excluded_domain.lower() in domain for excluded_domain in source_config['exclude_domains']):
                should_include = False
                continue
        
        # Check for keywords in URL (simple heuristic)
        if source_config.get('keywords'):
            url_lower = url.lower()
            if not any(keyword.lower() in url_lower for keyword in source_config['keywords']):
                should_include = False
                continue
        
        if should_include:
            filtered_urls.append(url)
    
    return filtered_urls


def extract_article_urls_from_page_tool(ctx: RunContext, page_content: str, base_url: str) -> List[str]:
    """Extract article URLs from a webpage content."""
    urls = []
    
    # Simple regex patterns for finding article links
    patterns = [
        r'href=["\']([^"\']*(?:article|story|news|post|blog)[^"\']*)["\']',
        r'href=["\']([^"\']*\/\d{4}\/\d{2}\/[^"\']*)["\']',  # Date-based URLs
        r'href=["\']([^"\']*\/news\/[^"\']*)["\']',  # News section URLs
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, page_content, re.IGNORECASE)
        for match in matches:
            if match.startswith('/'):
                full_url = f"{base_url.rstrip('/')}{match}"
            elif match.startswith('http'):
                full_url = match
            else:
                continue
            
            # Basic filtering
            if full_url not in urls and len(full_url) > 10:
                urls.append(full_url)
    
    return urls


def check_content_relevance_tool(ctx: RunContext, content: str, keywords: List[str]) -> ArticleFilterResult:
    """Check if article content is relevant based on keywords."""
    if not keywords:
        return ArticleFilterResult(
            included=True,
            reason="No keywords specified, including by default",
            confidence=1.0
        )
    
    content_lower = content.lower()
    found_keywords = []
    
    for keyword in keywords:
        if keyword.lower() in content_lower:
            found_keywords.append(keyword)
    
    if found_keywords:
        relevance_score = len(found_keywords) / len(keywords)
        return ArticleFilterResult(
            included=True,
            reason=f"Found keywords: {', '.join(found_keywords)}",
            confidence=min(relevance_score * 2, 1.0)  # Scale up relevance
        )
    else:
        return ArticleFilterResult(
            included=False,
            reason="No relevant keywords found",
            confidence=0.8
        )


# Tool functions that can be registered with FunctionToolset
ALL_NEWS_TOOL_FUNCTIONS = [
    tavily_search_tool,
    extract_urls_tool,
    crawl_website_tool,
    filter_by_date_tool,
    filter_urls_tool,
    extract_article_urls_from_page_tool,
    check_content_relevance_tool,
]
