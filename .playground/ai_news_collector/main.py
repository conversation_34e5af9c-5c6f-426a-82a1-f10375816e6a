#!/usr/bin/env python3
"""
News Collection System Main Script

This script demonstrates how to use the news collection system to gather 
articles from multiple sources as configured in a YAML file.
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

import yaml
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from ai_source_parser
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

from ai_news_collector.models import NewsCollectionConfig, RawArticle, NewsSource
from ai_news_collector.agents import create_news_collector
from ai_source_parser.news_pipeline_refactored import NewsExtractor, ExtractInput

# Load environment variables
load_dotenv()


async def load_config(config_path: str) -> NewsCollectionConfig:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config_data = yaml.safe_load(f)
    
    # Convert sources list to NewsSource objects
    if 'sources' in config_data:
        sources = []
        for source_data in config_data['sources']:
            source = NewsSource(**source_data)
            sources.append(source)
        config_data['sources'] = sources
    
    return NewsCollectionConfig(**config_data)


async def collect_and_process_news(config_path: str, use_multi_agent: bool = False) -> List[Any]:
    """
    Collect news from sources and process them through the extraction pipeline.
    
    Args:
        config_path: Path to the YAML configuration file
        use_multi_agent: Whether to use multi-agent approach
        
    Returns:
        List of processed NewsItem objects
    """
    print(f"Loading configuration from {config_path}")
    config = await load_config(config_path)
    
    print(f"Configuration loaded with {len(config.sources)} sources")
    enabled_sources = [s for s in config.sources if s.enabled]
    print(f"Enabled sources: {[s.name for s in enabled_sources]}")
    
    # Create news collector
    collector = create_news_collector(use_multi_agent=use_multi_agent)
    
    print("\n🔍 Starting news collection...")
    result = await collector.collect_news(config)
    
    print(f"\n📊 Collection Summary:")
    print(f"  Sources processed: {result.total_sources_processed}")
    print(f"  Sources successful: {result.total_sources_successful}")
    print(f"  Total articles collected: {result.total_articles_collected}")
    
    if result.extraction_results:
        print(f"\n📋 Per-source results:")
        for extraction_result in result.extraction_results:
            status = "✅" if extraction_result.success else "❌"
            print(f"  {status} {extraction_result.source_id}: {len(extraction_result.articles)} articles")
            if not extraction_result.success and extraction_result.error_message:
                print(f"    Error: {extraction_result.error_message}")
    
    # If we have articles, process them through the extraction pipeline
    if result.all_articles:
        print(f"\n🔬 Processing {len(result.all_articles)} articles through extraction pipeline...")
        
        # Create news extractor
        extractor = NewsExtractor()
        processed_items = []
        
        for i, article in enumerate(result.all_articles):
            print(f"  Processing article {i+1}/{len(result.all_articles)}: {article.title or 'Untitled'}")
            
            try:
                # Convert RawArticle to ExtractInput
                extract_input = ExtractInput(
                    url=str(article.url),
                    outlet=article.outlet,
                    raw_content=article.content
                )
                
                # Extract structured news item
                news_item = extractor.extract(extract_input)
                processed_items.append(news_item)
                
                print(f"    ✅ Processed: {news_item.content.title}")
                print(f"    📂 Category: {news_item.classification.category}")
                print(f"    🏷️  Type: {news_item.classification.type}")
                
            except Exception as e:
                print(f"    ❌ Error processing article: {str(e)}")
        
        print(f"\n✨ Successfully processed {len(processed_items)} out of {len(result.all_articles)} articles")
        return processed_items
    
    else:
        print("\n⚠️  No articles collected")
        return []


async def demo_simple_collection(config_path: str):
    """Demo of simple news collection without processing."""
    print("=== Demo: Simple News Collection ===\n")
    
    config = await load_config(config_path)
    collector = create_news_collector(use_multi_agent=False)
    
    result = await collector.collect_news(config)
    
    print(f"Collected {result.total_articles_collected} articles from {result.total_sources_successful} sources")
    
    # Show first few articles
    for i, article in enumerate(result.all_articles[:3]):
        print(f"\nArticle {i+1}:")
        print(f"  Title: {article.title}")
        print(f"  URL: {article.url}")
        print(f"  Source: {article.source_id}")
        print(f"  Content length: {len(article.content)} chars")
        print(f"  Published: {article.published_at}")


async def main():
    """Main function."""
    config_path = current_dir / "config.yaml"
    
    if not config_path.exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("Please create a config.yaml file with your news sources configuration.")
        return
    
    # Check for required environment variables
    if not os.getenv('TAVILY_API_KEY'):
        print("⚠️  Warning: TAVILY_API_KEY environment variable not set")
        print("Some extraction modes may not work without Tavily API key")
    
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key to use the extraction pipeline")
        return
    
    print("🚀 Carbon Regulation News Collection System\n")
    
    # Demo simple collection
    await demo_simple_collection(str(config_path))
    
    print("\n" + "="*60 + "\n")
    
    # Full collection and processing
    processed_items = await collect_and_process_news(str(config_path), use_multi_agent=False)
    
    if processed_items:
        print(f"\n📝 Sample processed item:")
        item = processed_items[0]
        print(f"  Title: {item.content.title}")
        print(f"  Summary: {item.content.summary}")
        print(f"  Category: {item.classification.category}")
        print(f"  Type: {item.classification.type}")
        print(f"  Jurisdictions: {item.classification.jurisdictions}")
        
        if item.content.key_points:
            print(f"  Key Points:")
            for point in item.content.key_points[:3]:  # Show first 3 points
                print(f"    • {point}")


if __name__ == "__main__":
    asyncio.run(main())
