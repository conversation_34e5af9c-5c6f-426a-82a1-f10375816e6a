from __future__ import annotations

from datetime import datetime, timedelta
from enum import Enum
from typing import List, Optional, Union, Dict, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, ConfigDict, AnyUrl


class SourceType(str, Enum):
    """Type of news source."""
    WEB_SEARCH = "web_search"
    SPECIFIC_SITE = "specific_site"
    RSS_FEED = "rss_feed"


class ExtractionMode(str, Enum):
    """How to extract articles from a source."""
    TAVILY_SEARCH = "tavily_search"
    WEBSITE_CRAWL = "website_crawl"
    DIRECT_EXTRACT = "direct_extract"


class NewsSource(BaseModel):
    """Configuration for a single news source."""
    
    model_config = ConfigDict(
        title="NewsSource",
        json_schema_extra={
            "description": "Configuration for collecting news from a specific source"
        }
    )
    
    id: str = Field(..., description="Unique identifier for this source")
    name: str = Field(..., description="Human-readable name for the source")
    source_type: SourceType = Field(..., description="Type of news source")
    extraction_mode: ExtractionMode = Field(..., description="How to extract news from this source")
    
    # Source configuration
    url: Optional[AnyUrl] = Field(default=None, description="Base URL for specific site sources")
    search_query: Optional[str] = Field(default=None, description="Search query for web search sources")
    
    # Filtering configuration
    time_window_hours: int = Field(default=24, description="Only collect news from the last N hours")
    max_articles: int = Field(default=10, description="Maximum number of articles to collect")
    
    # Source-specific extraction instructions
    extraction_instructions: str = Field(
        ...,
        description="Natural language instructions for the extraction agent on how to process this source"
    )
    
    # Optional filtering
    include_domains: Optional[List[str]] = Field(default=None, description="Only include articles from these domains")
    exclude_domains: Optional[List[str]] = Field(default=None, description="Exclude articles from these domains")
    keywords: Optional[List[str]] = Field(default=None, description="Keywords that must be present")
    
    # Configuration
    enabled: bool = Field(default=True, description="Whether this source is active")


class RawArticle(BaseModel):
    """Raw article data before processing."""
    
    model_config = ConfigDict(
        title="RawArticle",
        json_schema_extra={
            "description": "Raw article data extracted from a source"
        }
    )
    
    url: AnyUrl = Field(..., description="URL of the article")
    title: Optional[str] = Field(default=None, description="Article title if available")
    content: str = Field(..., description="Raw content/text of the article")
    published_at: Optional[datetime] = Field(default=None, description="Publication date if available")
    source_id: str = Field(..., description="ID of the source this came from")
    outlet: Optional[str] = Field(default=None, description="Publisher/outlet name")
    
    # Metadata
    collected_at: datetime = Field(default_factory=datetime.now, description="When this article was collected")
    content_length: int = Field(default=0, description="Length of content in characters")
    
    def __post_init__(self):
        self.content_length = len(self.content)


class ArticleFilterResult(BaseModel):
    """Result of filtering an article."""
    
    included: bool = Field(..., description="Whether the article should be included")
    reason: Optional[str] = Field(default=None, description="Reason for inclusion/exclusion")
    confidence: float = Field(default=1.0, description="Confidence in the decision (0-1)")


class ExtractionResult(BaseModel):
    """Result of extracting articles from a source."""
    
    model_config = ConfigDict(
        title="ExtractionResult",
        json_schema_extra={
            "description": "Result of extracting articles from a news source"
        }
    )
    
    source_id: str = Field(..., description="ID of the source")
    articles: List[RawArticle] = Field(default_factory=list, description="Extracted articles")
    success: bool = Field(..., description="Whether extraction was successful")
    error_message: Optional[str] = Field(default=None, description="Error message if extraction failed")
    
    # Metadata
    extraction_time: datetime = Field(default_factory=datetime.now, description="When extraction was performed")
    total_found: int = Field(default=0, description="Total number of articles found")
    total_filtered: int = Field(default=0, description="Number of articles after filtering")
    
    def __post_init__(self):
        self.total_found = len(self.articles)


class NewsCollectionConfig(BaseModel):
    """Configuration for news collection."""
    
    model_config = ConfigDict(
        title="NewsCollectionConfig",
        json_schema_extra={
            "description": "Configuration for the news collection system"
        }
    )
    
    sources: List[NewsSource] = Field(default_factory=list, description="List of news sources")
    
    # Global settings
    default_time_window_hours: int = Field(default=24, description="Default time window for news collection")
    max_concurrent_sources: int = Field(default=5, description="Maximum number of sources to process concurrently")
    retry_attempts: int = Field(default=3, description="Number of retry attempts for failed extractions")
    
    # Filtering settings
    global_exclude_domains: List[str] = Field(default_factory=list, description="Globally excluded domains")
    min_content_length: int = Field(default=500, description="Minimum content length in characters")
    max_content_length: int = Field(default=50000, description="Maximum content length in characters")


class NewsCollectionResult(BaseModel):
    """Final result of news collection across all sources."""
    
    model_config = ConfigDict(
        title="NewsCollectionResult",
        json_schema_extra={
            "description": "Final result of collecting news from all configured sources"
        }
    )
    
    extraction_results: List[ExtractionResult] = Field(default_factory=list, description="Results from each source")
    all_articles: List[RawArticle] = Field(default_factory=list, description="All collected articles")
    
    # Summary statistics
    total_sources_processed: int = Field(default=0, description="Number of sources processed")
    total_sources_successful: int = Field(default=0, description="Number of sources that succeeded")
    total_articles_collected: int = Field(default=0, description="Total articles collected")
    
    # Timing
    collection_started_at: datetime = Field(default_factory=datetime.now, description="When collection started")
    collection_completed_at: Optional[datetime] = Field(default=None, description="When collection completed")
    
    @property
    def results(self) -> List[ExtractionResult]:
        """Alias for extraction_results for backward compatibility."""
        return self.extraction_results
    
    @property
    def successful_sources(self) -> List[str]:
        """List of source IDs that succeeded."""
        return [r.source_id for r in self.extraction_results if r.success]
    
    @property
    def failed_sources(self) -> List[str]:
        """List of source IDs that failed."""
        return [r.source_id for r in self.extraction_results if not r.success]
    
    def finalize(self):
        """Finalize the collection result."""
        self.collection_completed_at = datetime.now()
        self.total_sources_processed = len(self.extraction_results)
        self.total_sources_successful = sum(1 for r in self.extraction_results if r.success)
        
        # Collect all articles
        self.all_articles = []
        for result in self.extraction_results:
            self.all_articles.extend(result.articles)
        
        self.total_articles_collected = len(self.all_articles)


# Tool function parameter models
class TavilySearchParams(BaseModel):
    """Parameters for Tavily search tool."""
    query: str = Field(..., description="Search query")
    time_range: str = Field(default="day", description="Time range for search")
    max_results: int = Field(default=10, description="Maximum number of results")
    include_domains: Optional[List[str]] = Field(default=None, description="Domains to include")
    exclude_domains: Optional[List[str]] = Field(default=None, description="Domains to exclude")


class ExtractUrlParams(BaseModel):
    """Parameters for URL extraction tool."""
    urls: List[str] = Field(..., description="URLs to extract content from")
    extract_depth: str = Field(default="basic", description="Extraction depth")


class DateFilterParams(BaseModel):
    """Parameters for date filtering tool."""
    content: str = Field(..., description="Content to analyze for dates")
    cutoff_hours: int = Field(default=24, description="Articles older than this many hours are filtered out")


class UrlFilterParams(BaseModel):
    """Parameters for URL filtering tool."""
    urls: List[str] = Field(..., description="URLs to filter")
    source_config: Dict[str, Any] = Field(..., description="Source configuration for filtering")
