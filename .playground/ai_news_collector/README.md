# AI News Collector

A sophisticated news collection system built with pydantic-ai that can gather news from multiple sources based on configurable instructions and pass them to a news processing pipeline.

## Features

- **Multi-source news collection**: Web search, specific websites, and RSS feeds
- **Configurable extraction modes**: Tavily search, website crawling, direct extraction
- **Intelligent filtering**: By date, domain, keywords, and content relevance
- **Multi-agent architecture**: Uses pydantic-ai agents with tools
- **YAML configuration**: Easy-to-maintain human-readable configuration
- **Integration ready**: Designed to work with the existing news pipeline

## Architecture

The system uses a multi-agent approach with pydantic-ai:

1. **NewsExtractionAgent**: Handles extraction from individual sources using various tools
2. **NewsCoordinatorAgent**: Orchestrates collection from multiple sources (optional)
3. **SimpleNewsCollector**: Simplified single-agent approach for direct use
4. **Tools**: Web search, URL extraction, content filtering, date checking, etc.

### Key Components

- **Models** (`models.py`): Pydantic models for configuration and data structures
- **Tools** (`tools.py`): Pydantic-ai tools for web search, extraction, and filtering
- **Agents** (`agents.py`): Pydantic-ai agents that use tools to collect news
- **Configuration** (`config.yaml`): YAML-based source configuration

## Installation

1. Ensure you have the required dependencies:
```bash
pip install pydantic-ai tavily-python httpx pyyaml python-dotenv
```

2. Set up environment variables:
```bash
export TAVILY_API_KEY="your_tavily_api_key"
export OPENAI_API_KEY="your_openai_api_key"
```

## Configuration

Create a `config.yaml` file to define your news sources. Example:

```yaml
# Global settings
default_time_window_hours: 24
max_concurrent_sources: 3
retry_attempts: 3

sources:
  - id: "reuters_clean_energy"
    name: "Reuters Clean Energy"
    source_type: "specific_site"
    extraction_mode: "website_crawl"
    url: "https://www.reuters.com/sustainability/clean-energy/"
    time_window_hours: 24
    max_articles: 10
    enabled: true
    extraction_instructions: |
      Visit the Reuters clean energy page and extract URLs of individual 
      news articles published in the last 24 hours. Focus on articles 
      related to clean energy, renewable energy, and carbon regulation.
    keywords:
      - "clean energy"
      - "renewable"
      - "carbon"
```

### Source Types

- **web_search**: Search the web using queries
- **specific_site**: Extract from specific website URLs
- **rss_feed**: Process RSS feeds (future enhancement)

### Extraction Modes

- **tavily_search**: Use Tavily search API
- **website_crawl**: Crawl website for article URLs
- **direct_extract**: Direct URL content extraction

## Usage

### Basic Usage

```python
import asyncio
from ai_news_collector import create_news_collector, NewsCollectionConfig

async def collect_news():
    # Load configuration
    config = NewsCollectionConfig.from_yaml("config.yaml")
    
    # Create collector
    collector = create_news_collector()
    
    # Collect news
    result = await collector.collect_news(config)
    
    print(f"Collected {result.total_articles_collected} articles")
    return result.all_articles

# Run collection
articles = asyncio.run(collect_news())
```

### Integration with News Pipeline

```python
from ai_news_collector import create_news_collector
from ai_source_parser.news_pipeline_refactored import NewsExtractor, ExtractInput

async def collect_and_process():
    # Collect raw articles
    collector = create_news_collector()
    result = await collector.collect_news(config)
    
    # Process through extraction pipeline
    extractor = NewsExtractor()
    processed_items = []
    
    for article in result.all_articles:
        extract_input = ExtractInput(
            url=str(article.url),
            outlet=article.outlet,
            raw_content=article.content
        )
        news_item = extractor.extract(extract_input)
        processed_items.append(news_item)
    
    return processed_items
```

### As a Tool for Other Agents

The news collector can be used as a tool in other pydantic-ai agents:

```python
from pydantic_ai import Agent
from ai_news_collector import create_news_collector_tool

# Create the tool
news_tool = create_news_collector_tool()

# Use in an agent
agent = Agent('openai:gpt-4o')

@agent.tool
async def collect_news(ctx, config_dict):
    """Collect news from configured sources."""
    return await news_tool(ctx, config_dict)
```

## Running the Demo

```bash
cd .playground/ai_news_collector
python main.py
```

This will:
1. Load the configuration from `config.yaml`
2. Collect news from all enabled sources
3. Process articles through the extraction pipeline
4. Display summary statistics and sample results

## Configuration Examples

### Web Search Source

```yaml
- id: "carbon_markets_search"
  name: "Carbon Markets News Search"
  source_type: "web_search"
  extraction_mode: "tavily_search"
  search_query: "carbon markets regulation policy news"
  time_window_hours: 24
  max_articles: 15
  extraction_instructions: |
    Search for recent news about carbon markets and regulation.
    Focus on regulatory updates and policy changes.
  include_domains:
    - "reuters.com"
    - "bloomberg.com"
  keywords:
    - "carbon market"
    - "carbon pricing"
```

### Specific Website Source

```yaml
- id: "reuters_clean_energy"
  name: "Reuters Clean Energy"
  source_type: "specific_site"
  extraction_mode: "website_crawl"
  url: "https://www.reuters.com/sustainability/clean-energy/"
  time_window_hours: 24
  max_articles: 10
  extraction_instructions: |
    Visit the Reuters clean energy page and extract article URLs.
    Filter for articles published in the last 24 hours.
```

## Key Features

### Intelligent Agent Instructions

Each source has `extraction_instructions` that provide natural language guidance to the extraction agent. This allows for flexible, source-specific extraction logic.

### Multi-Agent Architecture

- **NewsExtractionAgent**: Specialized for single-source extraction
- **NewsCoordinatorAgent**: Orchestrates multiple sources (can be used as a tool)
- **SimpleNewsCollector**: Streamlined approach for direct use

### Robust Error Handling

- Graceful handling of failed extractions
- Retry mechanisms for transient failures
- Detailed error reporting in results

### Flexible Filtering

- Time-based filtering (configurable hours)
- Domain inclusion/exclusion
- Keyword-based relevance filtering
- Content length limits

## Tools Available to Agents

The extraction agents have access to these tools:

- `tavily_search`: Web search using Tavily API
- `extract_urls`: Extract content from specific URLs
- `crawl_website`: Find article URLs on websites
- `filter_by_date`: Filter articles by publication date
- `filter_urls`: Filter URLs based on configuration
- `extract_article_urls`: Extract article links from page content
- `check_relevance`: Check content relevance using keywords

## Integration with Existing Pipeline

The system is designed to integrate seamlessly with the existing `news_pipeline_refactored.py`:

1. **Raw Collection**: AI News Collector gathers `RawArticle` objects
2. **Conversion**: Convert to `ExtractInput` format
3. **Processing**: Use existing `NewsExtractor` to create structured `NewsItem` objects

This separation allows for independent development and testing of the collection and processing stages.

## Environment Variables

- `TAVILY_API_KEY`: Required for web search functionality
- `OPENAI_API_KEY`: Required for agent operations
- `OPENROUTER_API_KEY`: Alternative to OpenAI (optional)

## Future Enhancements

- RSS feed support
- More sophisticated website crawling
- Integration with additional search APIs
- Caching and deduplication
- Scheduled collection with persistence
- Advanced filtering and relevance scoring
